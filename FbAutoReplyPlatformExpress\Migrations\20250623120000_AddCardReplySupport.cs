using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class AddCardReplySupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PrivateReplyType",
                table: "AppAutoReplyCampaigns",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "CardReplyDataJ<PERSON>",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(4000)",
                maxLength: 4000,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PrivateReplyType",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "CardReply<PERSON><PERSON><PERSON><PERSON>",
                table: "AppAutoReplyCampaigns");
        }
    }
}
